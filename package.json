{"name": "ai-chatbot-monorepo", "private": true, "version": "1.0.0", "description": "AI Chatbot with Node.js backend and React TypeScript frontend", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "vite", "dev:backend": "cd backend && npm run dev", "install:backend": "cd backend && npm install", "build": "vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "set:credentials": "cp ~/.aws/projects/internal ~/.aws/credentials", "deploy:frontend": "npm run set:credentials; ./scripts/deployment/frontend/deploy-frontend.sh", "deploy:frontend:update": "npm run set:credentials; ./scripts/deployment/frontend/update-frontend.sh", "deploy:frontend:status": "npm run set:credentials; ./scripts/deployment/frontend/frontend-status.sh", "deploy:backend": "npm run set:credentials; ./scripts/deployment/backend/deploy-backend.sh", "deploy:backend:update": "npm run set:credentials; ./scripts/deployment/backend/update-backend.sh", "deploy:backend:status": "npm run set:credentials; ./scripts/deployment/backend/backend-status.sh", "deploy:full": "npm run set:credentials; npm run deploy:frontend && npm run deploy:backend", "status:all": "npm run set:credentials; npm run deploy:frontend:status && npm run deploy:backend:status", "docs:add": "./scripts/deployment/backend/add-simple-api-docs.sh", "docs:view": "./scripts/deployment/backend/view-api-documentation.sh", "connect:backend": "./scripts/connect-backend.sh", "connect:local": "./scripts/connect-backend.sh local", "connect:deployed": "./scripts/connect-backend.sh deployed", "connect:start": "./scripts/connect-backend.sh start", "test:backend": "node scripts/test-backend-connection.js", "update:lambda-env": "npm run set:credentials; ./scripts/update-lambda-env.sh"}, "dependencies": {"lucide-react": "^0.525.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^20.19.10", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "sass": "^1.89.2", "typescript": "^5.2.2", "vite": "^7.0.4"}}