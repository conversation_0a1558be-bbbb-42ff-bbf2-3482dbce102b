import React, { useEffect, useRef } from 'react';
import { ImageIcon } from 'lucide-react';
import layoutStyles from '@/styles/layout.module.scss';

interface ResponseDisplayProps {
  response: string;
  imageAnalyzed?: boolean;
  className?: string;
}

const ResponseDisplay: React.FC<ResponseDisplayProps> = ({
  response,
  imageAnalyzed = false,
  className,
}) => {
  const responseRef = useRef<HTMLDivElement>(null);

  // Scroll into view when response appears
  useEffect(() => {
    if (responseRef.current) {
      responseRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [response]);

  const formatResponse = (text: string) => {
    // Replace line breaks
    let formattedText = text.replace(/\n/g, '<br>');
    
    // Highlight questions (Q:)
    formattedText = formattedText.replace(
      /Q:\s*([^<]*?)(?=<br>|$)/gi,
      '<span class="question-highlight"><span class="question-icon">Q:</span><strong></strong> $1</span>'
    );
    
    // Highlight assumptions (remove "Assume:" prefix and just show the assumption)
    formattedText = formattedText.replace(
      /Assume:\s*([^<]*?)(?=<br>|$)/gi,
      '<span class="assumption-highlight"><span class="assumption-icon">•</span>$1</span>'
    );
    
    return formattedText;
  };

  const isError = response.includes('Error:');

  return (
    <div 
      ref={responseRef}
      className={`${layoutStyles.responseCard} ${layoutStyles.visible} ${className || ''}`}
      role="region"
      aria-label="B&T AI Helper"
    >
      <h3 className={layoutStyles.responseHeader}>AI Analysis</h3>
      
      {imageAnalyzed && (
        <div className={layoutStyles.imageAnalyzedBadge}>
          <ImageIcon size={16} style={{ marginRight: '6px' }} />
          Image analyzed
        </div>
      )}
      
      <div 
        className={isError ? layoutStyles.errorMessage : layoutStyles.responseContent}
        dangerouslySetInnerHTML={{ __html: formatResponse(response) }}
      />
    </div>
  );
};

export { ResponseDisplay };
export default ResponseDisplay;
